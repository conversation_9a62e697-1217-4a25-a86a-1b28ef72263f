/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_ROBOT_BASE_H
#define AUBO_COFFEE_SERVICE_ROBOT_BASE_H

#include <string>
#include <memory>

#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

namespace aubo {

/**
 * @class RobotBase
 * @brief 机器人基类
 *
 * 提供所有机器人的通用功能实现
 */
class RobotBase {
public:
    /**
     * @brief 构造函数
     * @param robot_name 机器人名称
     * @param host 机器人IP地址
     * @param port 机器人端口
     */
    RobotBase(const std::string& robot_name, const std::string& host, int port);

    /**
     * @brief 虚析构函数
     */
    virtual ~RobotBase();

    // === 通用功能 - 基类直接实现，不需要重写 ===

    /**
     * @brief 初始化机器人
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机器人连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 获取机器人状态
     * @return 状态信息字符串
     */
    std::string get_status() const;

    /**
     * @brief 检查机器人是否已连接
     * @return 已连接返回true
     */
    bool is_connected() const;

    // 子类自己实现各自特有的功能，不需要在基类中定义虚函数

    /**
     * @brief 获取机器人名称
     * @return 机器人名称
     */
    const std::string& get_name() const { return robot_name_; }

protected:
    /**
     * @brief 连接到机器人
     * @return 连接成功返回true
     */
    bool connect();

    /**
     * @brief 断开机器人连接
     * @return 断开成功返回true
     */
    bool disconnect();

    /**
     * @brief 检查机器人状态
     * @return 状态正常返回true
     */
    bool check_robot_state();

protected:
    std::string robot_name_;        ///< 机器人名称
    std::string host_;              ///< 机器人IP地址
    int port_;                      ///< 机器人端口
    bool is_connected_;             ///< 连接状态
    bool is_initialized_;           ///< 初始化状态

    // 机器人服务接口
    ServiceInterface robot_service_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_ROBOT_BASE_H
