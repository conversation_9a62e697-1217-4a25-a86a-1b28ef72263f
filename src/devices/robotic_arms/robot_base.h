/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_ROBOT_BASE_H
#define AUBO_COFFEE_SERVICE_ROBOT_BASE_H

#include <string>
#include <memory>
#include "../../core/coffee_order.h"

namespace aubo {

/**
 * @class RobotBaseImpl
 * @brief 机器人基础实现类
 *
 * 提供所有机器人的通用实现功能，供具体机器人的Impl类继承
 */
class RobotBaseImpl {
public:
    /**
     * @brief 构造函数
     * @param robot_name 机器人名称
     * @param host 机器人IP地址
     * @param port 机器人端口
     */
    RobotBaseImpl(const std::string& robot_name, const std::string& host, int port);

    /**
     * @brief 虚析构函数
     */
    virtual ~RobotBaseImpl();

    /**
     * @brief 初始化机器人
     * @return 初始化成功返回true
     */
    virtual bool init();

    /**
     * @brief 关闭机器人连接
     * @return 关闭成功返回true
     */
    virtual bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    virtual bool emergency_stop();

    /**
     * @brief 移动到初始位置
     * @return 移动成功返回true
     */
    virtual bool move_to_home();

    /**
     * @brief 移动到准备位置
     * @return 移动成功返回true
     */
    virtual bool move_to_ready();

    /**
     * @brief 获取机器人状态
     * @return 状态信息字符串
     */
    virtual std::string get_status() const;

    /**
     * @brief 检查机器人是否已连接
     * @return 已连接返回true
     */
    virtual bool is_connected() const;

    /**
     * @brief 获取机器人名称
     * @return 机器人名称
     */
    const std::string& get_name() const { return robot_name_; }

protected:
    /**
     * @brief 连接到机器人
     * @return 连接成功返回true
     */
    virtual bool connect();

    /**
     * @brief 断开机器人连接
     * @return 断开成功返回true
     */
    virtual bool disconnect();

    /**
     * @brief 执行移动到指定位置
     * @param joint_positions 关节位置数组
     * @param position_count 位置数量
     * @return 移动成功返回true
     */
    virtual bool move_to_position(const double* joint_positions, int position_count);

    /**
     * @brief 等待移动完成
     * @return 移动完成返回true
     */
    virtual bool wait_for_motion_complete();

protected:
    std::string robot_name_;        ///< 机器人名称
    std::string host_;              ///< 机器人IP地址
    int port_;                      ///< 机器人端口
    bool is_connected_;             ///< 连接状态
    bool is_initialized_;           ///< 初始化状态

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @class RobotBase
 * @brief 机器人接口基类
 *
 * 提供所有机器人的通用接口
 */
class RobotBase {
public:
    /**
     * @brief 虚析构函数
     */
    virtual ~RobotBase() = default;

    /**
     * @brief 初始化机器人
     * @return 初始化成功返回true
     */
    virtual bool init() = 0;

    /**
     * @brief 关闭机器人连接
     * @return 关闭成功返回true
     */
    virtual bool shutdown() = 0;

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    virtual bool emergency_stop() = 0;

    /**
     * @brief 移动到初始位置
     * @return 移动成功返回true
     */
    virtual bool move_to_home() = 0;

    /**
     * @brief 移动到准备位置
     * @return 移动成功返回true
     */
    virtual bool move_to_ready() = 0;

    /**
     * @brief 获取机器人状态
     * @return 状态信息字符串
     */
    virtual std::string get_status() const = 0;

    /**
     * @brief 检查机器人是否已连接
     * @return 已连接返回true
     */
    virtual bool is_connected() const = 0;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_ROBOT_BASE_H
