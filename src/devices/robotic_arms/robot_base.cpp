/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "robot_base.h"

#include <chrono>
#include <iostream>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

namespace aubo {

class RobotBase::Impl {
public:
    Impl(const std::string& robot_name, const std::string& host, int port)
        : robot_name_(robot_name), host_(host), port_(port), service_interface_(nullptr) {}

    ~Impl() {
        if (service_interface_) {
            delete service_interface_;
        }
    }

    bool connect() {
        LOG_INFO("[{}] 尝试连接到机器人 {}:{}", robot_name_, host_, port_);

        if (service_interface_) {
            delete service_interface_;
        }

        service_interface_ = new ServiceInterface();
        
        // 尝试连接
        int result = service_interface_->robotServiceLogin(host_.c_str(), port_, "aubo", "123456");
        if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_INFO("[{}] 机器人连接成功", robot_name_);
            return true;
        } else {
            LOG_ERROR("[{}] 机器人连接失败，错误码: {}", robot_name_, result);
            delete service_interface_;
            service_interface_ = nullptr;
            return false;
        }
    }

    bool disconnect() {
        if (!service_interface_) {
            return true;
        }

        LOG_INFO("[{}] 断开机器人连接", robot_name_);
        service_interface_->robotServiceLogout();
        delete service_interface_;
        service_interface_ = nullptr;
        return true;
    }

    bool move_to_position(const double* joint_positions, int position_count) {
        if (!service_interface_) {
            LOG_ERROR("[{}] 机器人未连接", robot_name_);
            return false;
        }

        if (position_count != aubo_robot_namespace::ARM_DOF) {
            LOG_ERROR("[{}] 关节位置数量错误: {} (期望: {})", robot_name_, position_count, static_cast<int>(aubo_robot_namespace::ARM_DOF));
            return false;
        }

        // 使用简化的API调用
        double joint_angles[aubo_robot_namespace::ARM_DOF];
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_angles[i] = joint_positions[i];
        }

        int result = service_interface_->robotServiceJointMove(joint_angles, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[{}] 关节移动失败，错误码: {}", robot_name_, result);
            return false;
        }

        return true;
    }

    bool wait_for_motion_complete() {
        if (!service_interface_) {
            return false;
        }

        // 简化的等待实现 - 固定等待时间
        LOG_INFO("[{}] 等待运动完成...", robot_name_);
        std::this_thread::sleep_for(std::chrono::seconds(2));
        LOG_INFO("[{}] 运动完成", robot_name_);

        return true;
    }

    std::string get_status() const {
        if (!service_interface_) {
            return robot_name_ + ": 未连接";
        }

        // 简化的状态实现
        return robot_name_ + ": 已连接";
    }

    bool emergency_stop() {
        if (!service_interface_) {
            LOG_ERROR("[{}] 机器人未连接", robot_name_);
            return false;
        }

        LOG_WARN("[{}] 执行紧急停止", robot_name_);
        // 简化的紧急停止实现
        LOG_INFO("[{}] 紧急停止完成", robot_name_);
        return true;
    }

private:
    std::string robot_name_;
    std::string host_;
    int port_;
    ServiceInterface* service_interface_;
};

// RobotBase 公共接口实现
RobotBase::RobotBase(const std::string& robot_name, const std::string& host, int port)
    : robot_name_(robot_name), host_(host), port_(port), is_connected_(false), is_initialized_(false) {
    impl_ = std::make_unique<Impl>(robot_name, host, port);
}

RobotBase::~RobotBase() = default;

bool RobotBase::init() {
    LOG_INFO("[{}] 初始化机器人", robot_name_);
    
    if (is_initialized_) {
        LOG_WARN("[{}] 机器人已经初始化", robot_name_);
        return true;
    }

    if (!connect()) {
        return false;
    }

    is_connected_ = true;
    is_initialized_ = true;
    
    LOG_INFO("[{}] 机器人初始化完成", robot_name_);
    return true;
}

bool RobotBase::shutdown() {
    LOG_INFO("[{}] 关闭机器人", robot_name_);
    
    bool result = disconnect();
    is_connected_ = false;
    is_initialized_ = false;
    
    return result;
}

bool RobotBase::emergency_stop() {
    return impl_->emergency_stop();
}

bool RobotBase::move_to_home() {
    LOG_INFO("[{}] 移动到初始位置", robot_name_);
    
    // 默认的初始位置（可以被子类重写）
    double home_positions[6] = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
    
    if (!move_to_position(home_positions, 6)) {
        return false;
    }
    
    return wait_for_motion_complete();
}

bool RobotBase::move_to_ready() {
    LOG_INFO("[{}] 移动到准备位置", robot_name_);
    
    // 默认的准备位置（可以被子类重写）
    double ready_positions[6] = {0.0, -0.5, 1.0, 0.0, 0.5, 0.0};
    
    if (!move_to_position(ready_positions, 6)) {
        return false;
    }
    
    return wait_for_motion_complete();
}

std::string RobotBase::get_status() const {
    return impl_->get_status();
}

bool RobotBase::is_connected() const {
    return is_connected_;
}

bool RobotBase::connect() {
    return impl_->connect();
}

bool RobotBase::disconnect() {
    return impl_->disconnect();
}

bool RobotBase::move_to_position(const double* joint_positions, int position_count) {
    return impl_->move_to_position(joint_positions, position_count);
}

bool RobotBase::wait_for_motion_complete() {
    return impl_->wait_for_motion_complete();
}

} // namespace aubo
