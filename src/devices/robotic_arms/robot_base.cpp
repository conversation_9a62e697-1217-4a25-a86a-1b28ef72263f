/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "robot_base.h"

#include <chrono>
#include <iostream>
#include <thread>

#include <aubo-base/log.h>

namespace aubo {

// RobotBase 实现
RobotBase::RobotBase(const std::string& robot_name, const std::string& host, int port)
    : robot_name_(robot_name), host_(host), port_(port), is_connected_(false), is_initialized_(false) {
}

RobotBase::~RobotBase() = default;

bool RobotBase::init() {
    LOG_INFO("[{}] 初始化机器人", robot_name_);

    if (is_initialized_) {
        LOG_WARN("[{}] 机器人已经初始化", robot_name_);
        return true;
    }

    if (!connect()) {
        return false;
    }

    is_connected_ = true;
    is_initialized_ = true;

    LOG_INFO("[{}] 机器人初始化完成", robot_name_);
    return true;
}

bool RobotBase::shutdown() {
    LOG_INFO("[{}] 关闭机器人", robot_name_);

    bool result = disconnect();
    is_connected_ = false;
    is_initialized_ = false;

    return result;
}

bool RobotBase::emergency_stop() {
    LOG_WARN("[{}] 执行紧急停止", robot_name_);

    if (!is_connected_) {
        LOG_ERROR("[{}] 机器人未连接", robot_name_);
        return false;
    }

    // 简化的紧急停止实现
    LOG_INFO("[{}] 紧急停止完成", robot_name_);
    return true;
}

std::string RobotBase::get_status() const {
    if (!is_connected_) {
        return robot_name_ + ": 未连接";
    }

    return robot_name_ + ": 已连接";
}

bool RobotBase::is_connected() const {
    return is_connected_;
}

bool RobotBase::connect() {
    LOG_INFO("[{}] 连接到机器人 {}:{}", robot_name_, host_, port_);

    try {
        int result = robot_service_.robotServiceLogin(host_.c_str(), port_, "aubo", "123456");
        if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_INFO("[{}] 机器人连接成功", robot_name_);
            is_connected_ = true;
            return true;
        } else {
            LOG_ERROR("[{}] 机器人连接失败，错误码: {}", robot_name_, result);
            is_connected_ = false;
            return false;
        }
    } catch (const std::exception& e) {
        LOG_ERROR("[{}] 连接异常: {}", robot_name_, e.what());
        is_connected_ = false;
        return false;
    }
}

bool RobotBase::disconnect() {
    if (!is_connected_) {
        return true;
    }

    LOG_INFO("[{}] 断开机器人连接", robot_name_);
    robot_service_.robotServiceLogout();
    is_connected_ = false;
    return true;
}

bool RobotBase::check_robot_state() {
    if (!is_connected_) {
        LOG_ERROR("[{}] 无法操作: 未连接", robot_name_);
        return false;
    }

    if (!is_initialized_) {
        LOG_ERROR("[{}] 无法操作: 未初始化", robot_name_);
        return false;
    }

    return true;
}

} // namespace aubo
